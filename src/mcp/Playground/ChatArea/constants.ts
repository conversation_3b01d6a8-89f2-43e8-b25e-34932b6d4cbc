export const UI_DIMENSIONS = {
    FONT_SIZE_SMALL: 12,
    FONT_SIZE_MEDIUM: 14,
    FONT_SIZE_LARGE: 16,
    FONT_SIZE_XLARGE: 38,

    FONT_WEIGHT_NORMAL: 400,
    FONT_WEIGHT_MEDIUM: 500,

    SPACING_SMALL: 4,
    SPACING_MEDIUM: 8,
    SPACING_LARGE: 12,
    SPACING_XLARGE: 16,
    SPACING_XXLARGE: 24,
    SPACING_XXXLARGE: 32,

    PADDING_XS: '5px 12px',
    PADDING_SMALL: '8px 0',
    PADDING_MEDIUM: '10px 12px',
    PADDING_LARGE: '12px 16px',

    MARGIN_SMALL: '4px',
    MARGIN_MEDIUM: '8px',
    MARGIN_LARGE: '16px',
    MARGIN_XLARGE: '24px 106px 0 53px',

    BORDER_RADIUS_SMALL: 2,
    BORDER_RADIUS_MEDIUM: 6,
    BORDER_RADIUS_LARGE: 12,
    MIN_HEIGHT_SMALL: 32,
    MIN_HEIGHT_MEDIUM: 42,
    MIN_HEIGHT_LARGE: 60,
    MAX_HEIGHT_MEDIUM: 118,
    MAX_HEIGHT_LARGE: 200,
    SCROLLBAR_WIDTH: 4,

    POSITION_BOTTOM: 80,
    POSITION_SIDE: 105,
    POSITION_SIDE_SMALL: 106,
    POSITION_LEFT: 52,
    POSITION_LEFT_SMALL: 53,

    FULL_WIDTH: '100%',
    FIT_CONTENT: 'fit-content',

    DOT_SIZE: 4,
} as const;

export const RESPONSIVE_SPACING = {
    INPUT_CONTAINER: {
        MARGIN_BOTTOM: 12,
        PADDING_HORIZONTAL: 15,
        MAX_WIDTH: 1200,
        MIN_WIDTH: 1040,
    },
    MESSAGE_PANEL: {
        MARGIN_TOP: 24,
        MARGIN_HORIZONTAL: 106,
        MARGIN_LEFT: 53,
        MIN_WIDTH: 940,
    },
    BREAKPOINTS: {
        NARROW: 1200,
        WIDE: 1400,
    },
} as const;

export const UI_COLORS = {
    WHITE: '#fff',
    BLACK: '#000',
    TRANSPARENT: 'transparent',

    BORDER_COLOR: '#d9d9d9',
    BORDER_COLOR_PRIMARY: '#1890ff',

    BACKGROUND_DISABLED: '#f5f5f5',
    BACKGROUND_USER_BUBBLE: '#CCE5FF',
    BACKGROUND_PRIMARY: '#1890ff',

    TEXT_PRIMARY: '#262626',
    TEXT_SECONDARY: '#666',
    TEXT_TERTIARY: '#999',
    TEXT_WHITE: '#fff',
    TEXT_GRAY: 'var(--color-gray-10)',

    SCROLLBAR_THUMB: '#d9d9d9',
} as const;

export const TEXT_LABELS = {
    COPY_SUCCESS: '复制成功',
    COPY: '复制',

    THINKING: '思考中',
    ESTIMATED_TIME: '（预计用时15-30秒）',
    AI_ASSISTANT: 'AI助手',

    CLEAR_CONVERSATION: '清空对话',
    CLEAR_CONVERSATION_CONFIRM: '确定要清空所有对话记录吗？此操作不可撤销。',
    CONFIRM: '确定',
    CANCEL: '取消',

    PROCESSING_MESSAGE: '正在处理消息...',
    ERROR_RETRY: '发生错误，请稍后重试',
    PLACEHOLDER_TEXT: '完成参数配置后对话',
} as const;

export const ANIMATION_VALUES = {
    TRANSITION_FAST: '0.3s',
    TRANSITION_MEDIUM: '0.2s',
    COPY_NOTIFICATION_DURATION: 2500,

    DOT_ANIMATION_DURATION: '1.4s',
    DOT_ANIMATION_DELAY_1: '0.2s',
    DOT_ANIMATION_DELAY_2: '0.4s',

    CUBIC_BEZIER: 'cubic-bezier(0.4, 0, 0.2, 1)',

    OPACITY_HIDDEN: 0,
    OPACITY_PARTIAL: 0.6,
    OPACITY_VISIBLE: 1,
} as const;

export const LAYOUT_VALUES = {
    Z_INDEX_LOW: 1,
    Z_INDEX_MEDIUM: 2,
    Z_INDEX_HIGH: 1000,

    POSITION_ABSOLUTE: 'absolute',

    OVERFLOW_HIDDEN: 'hidden',
    OVERFLOW_AUTO: 'auto',

    TRANSFORM_SHOW: '0',
    TRANSFORM_HIDE: '-100%',

    FLEX_1: 1,
    WIDTH_100: '100%',
    HEIGHT_100: '100%',
} as const;

export const CONFIG_VALUES = {
    DEFAULT_AGENT_ID: 17,

    SPIN_SIZE_SMALL: 'small',

    BORDER_NONE: 'none',
    BOX_SHADOW_NONE: 'none !important',

    RESIZE_NONE: 'none',
    BORDER_RADIUS_50: '50%',
    ANIMATION_INFINITE: 'infinite',
} as const;
