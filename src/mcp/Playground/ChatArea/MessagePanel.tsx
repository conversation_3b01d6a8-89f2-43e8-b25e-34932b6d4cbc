import {useRef, useImperativeHandle, forwardRef, useCallback} from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useMCPMessages} from '@/regions/mcp/mcpPlaygroundChat';
import {
    UI_COLORS,
    LAYOUT_VALUES,
    ANIMATION_VALUES,
    RESPONSIVE_SPACING,
} from './constants';
import MessageList from './MessageList';
import WelcomeScreen from './WelcomeScreen';

interface MessagePanelProps {
    show: boolean;
}

export interface MessagePanelRef {
    scrollToBottom: () => void;
}

const Container = styled.div<{ show: boolean }>`
    position: ${LAYOUT_VALUES.POSITION_ABSOLUTE};
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    min-width: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MIN_WIDTH}px;
    transform: translateX(${props => (props.show ? LAYOUT_VALUES.TRANSFORM_SHOW : LAYOUT_VALUES.TRANSFORM_HIDE)});
    transition: transform ${ANIMATION_VALUES.TRANSITION_FAST} ${ANIMATION_VALUES.CUBIC_BEZIER};
    background-color: ${UI_COLORS.WHITE};
    z-index: ${props => (props.show ? LAYOUT_VALUES.Z_INDEX_MEDIUM : LAYOUT_VALUES.Z_INDEX_LOW)};
`;

const MessagesContainer = styled.div`
    flex: ${LAYOUT_VALUES.FLEX_1};
    margin: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_TOP}px
            ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_HORIZONTAL}px
            0
            ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_LEFT}px;
    overflow-y: ${LAYOUT_VALUES.OVERFLOW_AUTO};

    @media (max-width: ${RESPONSIVE_SPACING.BREAKPOINTS.NARROW}px) {
        margin: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_TOP}px
                ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_LEFT}px
                0
                ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_LEFT}px;
    }

    @media (min-width: ${RESPONSIVE_SPACING.BREAKPOINTS.WIDE}px) {
        margin: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_TOP}px auto 0 auto;
        max-width: ${RESPONSIVE_SPACING.INPUT_CONTAINER.MAX_WIDTH}px;
    }
`;

const MessagePanel = forwardRef<MessagePanelRef, MessagePanelProps>(
    ({show}, ref) => {
        const messages = useMCPMessages();
        const messagesEndRef = useRef<HTMLDivElement>(null);
        const hasMessages = messages.length > 0;

        const scrollToBottom = useCallback(
            () => {
                messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
            },
            []
        );

        useImperativeHandle(
            ref,
            () => ({
                scrollToBottom,
            }),
            [scrollToBottom]
        );

        return (
            <Container show={show}>
                <Flex vertical style={{height: '100%'}}>
                    {hasMessages ? (
                        <MessagesContainer>
                            <MessageList />
                            <div ref={messagesEndRef} />
                        </MessagesContainer>
                    ) : (
                        <WelcomeScreen />
                    )}
                </Flex>
            </Container>
        );
    }
);

MessagePanel.displayName = 'MessagePanel';

export default MessagePanel;
