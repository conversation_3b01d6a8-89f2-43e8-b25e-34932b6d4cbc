/* eslint-disable max-lines */
import {useEffect, useRef, useCallback, useMemo} from 'react';
import styled from '@emotion/styled';
import {Button, Modal} from '@panda-design/components';
import {Flex} from 'antd';
import {last} from 'lodash';
import {
    useMCPMessages,
    useMCPChatStatus,
    clearMCPMessages,
    setMCPConversationId,
    useMCPChat,
    useMCPMessage,
} from '@/regions/mcp/mcpPlaygroundChat';
import {IconDelete} from '@/icons/mcp';
import {
    ConversationIdProvider,
    useConversationId,
} from '@/components/Chat/Provider/ConversationIdProvider';
import {
    UI_DIMENSIONS,
    UI_COLORS,
    LAYOUT_VALUES,
    CONFIG_VALUES,
    TEXT_LABELS,
    RESPONSIVE_SPACING,
} from './constants';
import MessagePanel, {MessagePanelRef} from './MessagePanel';
import ChatMessageInput from './ChatMessageInput';
import {useChatMessage} from './hooks/useChatMessage';
import {AgentIdProvider} from './AgentIdProvider';

const Container = styled.div`
    height: ${LAYOUT_VALUES.HEIGHT_100};
    min-width: ${RESPONSIVE_SPACING.INPUT_CONTAINER.MIN_WIDTH}px;
    background: ${UI_COLORS.WHITE};
`;

const Content = styled.div`
    flex: ${LAYOUT_VALUES.FLEX_1};
    position: relative;
    overflow: ${LAYOUT_VALUES.OVERFLOW_HIDDEN};
`;

const ActionBarContainer = styled.div`
    position: ${LAYOUT_VALUES.POSITION_ABSOLUTE};
    bottom: ${UI_DIMENSIONS.POSITION_BOTTOM}px;
    left: 0;
    right: 0;
    z-index: ${LAYOUT_VALUES.Z_INDEX_HIGH};
    padding: 0;
    margin-right: ${RESPONSIVE_SPACING.MESSAGE_PANEL.MARGIN_HORIZONTAL}px;
`;

const InputContainer = styled.div`
    position: ${LAYOUT_VALUES.POSITION_ABSOLUTE};
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto ${RESPONSIVE_SPACING.INPUT_CONTAINER.MARGIN_BOTTOM}px auto;
    padding: 0 ${RESPONSIVE_SPACING.INPUT_CONTAINER.PADDING_HORIZONTAL}px;
    z-index: ${LAYOUT_VALUES.Z_INDEX_HIGH};
    max-width: ${RESPONSIVE_SPACING.INPUT_CONTAINER.MAX_WIDTH}px;

    @media (max-width: ${RESPONSIVE_SPACING.BREAKPOINTS.NARROW}px) {
        min-width: ${RESPONSIVE_SPACING.INPUT_CONTAINER.MIN_WIDTH}px;
    }

    @media (min-width: ${RESPONSIVE_SPACING.BREAKPOINTS.WIDE}px) {
        margin: 0 auto ${RESPONSIVE_SPACING.INPUT_CONTAINER.MARGIN_BOTTOM}px auto;
    }
`;

const ChatArea = () => {
    const conversationId = useConversationId();
    const messages = useMCPMessages();
    const status = useMCPChatStatus();
    const messagePanelRef = useRef<MessagePanelRef>(null);
    const {messageIds} = useMCPChat();
    const lastMessageId =
        messageIds && messageIds.length > 0 ? last(messageIds) : null;
    const lastMessage = useMCPMessage(lastMessageId);
    const lastElementContent = useMemo(
        () => {
            const elements = lastMessage?.elements;
            if (!elements || elements.length === 0) {
                return null;
            }
            return JSON.stringify(last(elements));
        },
        [lastMessage?.elements]
    );

    const handleScrollToBottom = useCallback(
        () => {
            messagePanelRef.current?.scrollToBottom();
        },
        []
    );
    useEffect(
        () => {
            if (lastElementContent) {
                handleScrollToBottom();
            }
        },
        [lastElementContent, handleScrollToBottom]
    );

    const {sendMessage, stopGeneration} =
        useChatMessage(handleScrollToBottom);

    const handleClearMessages = useCallback(
        () => {
            Modal.confirm({
                title: TEXT_LABELS.CLEAR_CONVERSATION,
                content: TEXT_LABELS.CLEAR_CONVERSATION_CONFIRM,
                okText: TEXT_LABELS.CONFIRM,
                cancelText: TEXT_LABELS.CANCEL,
                onOk: () => {
                    clearMCPMessages();
                    setMCPConversationId('');
                    stopGeneration();
                },
            });
        },
        [stopGeneration]
    );

    const isDisabled = status === 'loading';

    const getDisabledReason = () => {
        if (status === 'loading') {
            return TEXT_LABELS.PROCESSING_MESSAGE;
        }
        if (status === 'error') {
            return TEXT_LABELS.ERROR_RETRY;
        }
        return '';
    };

    return (
        <AgentIdProvider agentId={CONFIG_VALUES.DEFAULT_AGENT_ID}>
            <ConversationIdProvider conversationId={conversationId}>
                <Container>
                    <Flex vertical style={{height: '100%'}}>
                        <Content>
                            <MessagePanel ref={messagePanelRef} show />
                            {messages.length > 0 && (
                                <ActionBarContainer>
                                    <Flex justify="flex-end">
                                        <Button
                                            icon={<IconDelete />}
                                            style={{border: CONFIG_VALUES.BORDER_NONE}}
                                            onClick={handleClearMessages}
                                        >
                                            {TEXT_LABELS.CLEAR_CONVERSATION}
                                        </Button>
                                    </Flex>
                                </ActionBarContainer>
                            )}
                            <InputContainer>
                                <ChatMessageInput
                                    disabled={isDisabled}
                                    disabledReason={getDisabledReason()}
                                    onSend={sendMessage}
                                    onStop={stopGeneration}
                                    isGenerating={status === 'loading'}
                                    placeholder={TEXT_LABELS.PLACEHOLDER_TEXT}
                                />
                            </InputContainer>
                        </Content>
                    </Flex>
                </Container>
            </ConversationIdProvider>
        </AgentIdProvider>
    );
};

export default ChatArea;
