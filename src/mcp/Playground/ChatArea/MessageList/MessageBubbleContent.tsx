import {Flex, Spin} from 'antd';
import styled from '@emotion/styled';
import {Elements} from '@/components/Chat/Element';
import {ChatMessage} from '@/types/staff/chat';
import {
    UI_DIMENSIONS,
    UI_COLORS,
    TEXT_LABELS,
    CONFIG_VALUES,
} from '../constants';
import {
    ThinkingIndicator,
    EstimatedTime,
    DotLoading,
} from './styles';

const UserContainer = styled.div`
    gap: ${UI_DIMENSIONS.SPACING_SMALL}px;
    justify-content: flex-end;
    padding: ${UI_DIMENSIONS.PADDING_XS};
    margin-left: ${UI_DIMENSIONS.SPACING_XXXLARGE}px;
    background: ${UI_COLORS.BACKGROUND_USER_BUBBLE};
    border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_MEDIUM}px;
`;
interface MessageBubbleContentProps {
    message: ChatMessage;
    isUser: boolean;
}

const MessageBubbleContent = ({message, isUser}: MessageBubbleContentProps) => {
    const isStreaming = message.stream && !message.finish;
    const isEmpty = !message?.elements?.length && isStreaming;

    if (isUser) {
        return (
            <Flex justify="end">
                <Flex vertical gap={UI_DIMENSIONS.SPACING_SMALL} justify="flex-end">
                    <UserContainer>
                        <Elements items={message?.elements} />
                    </UserContainer>
                </Flex>
            </Flex>
        );
    }

    return (
        <Flex vertical gap={UI_DIMENSIONS.SPACING_LARGE}>
            {isEmpty && (
                <Flex align="center" gap={UI_DIMENSIONS.SPACING_MEDIUM}>
                    <ThinkingIndicator>
                        <Spin size={CONFIG_VALUES.SPIN_SIZE_SMALL} />
                        <span>{TEXT_LABELS.THINKING}</span>
                        <EstimatedTime>{TEXT_LABELS.ESTIMATED_TIME}</EstimatedTime>
                        <DotLoading />
                    </ThinkingIndicator>
                </Flex>
            )}
            <Elements items={message?.elements} />
        </Flex>
    );
};

export default MessageBubbleContent;
