import {Typography} from 'antd';
import styled from '@emotion/styled';
import {keyframes} from '@emotion/react';
import {
    UI_DIMENSIONS,
    UI_COLORS,
    LAYOUT_VALUES,
    CONFIG_VALUES,
    ANIMATION_VALUES,
} from '../constants';

const {Text} = Typography;

export const ToolCallContainer = styled.div<{ isUser: boolean }>`
    width: ${LAYOUT_VALUES.WIDTH_100};
    margin-top: ${UI_DIMENSIONS.SPACING_MEDIUM}px;
    margin-bottom: ${UI_DIMENSIONS.SPACING_MEDIUM}px;
    padding: 0 44px;
`;

export const MessageBubble = styled.div<{ isUser: boolean }>`
    padding: ${UI_DIMENSIONS.PADDING_LARGE};
    border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_LARGE}px;
    background: ${props => (props.isUser ? UI_COLORS.BACKGROUND_PRIMARY : UI_COLORS.TRANSPARENT)};
    color: ${props => (props.isUser ? UI_COLORS.TEXT_WHITE : UI_COLORS.TEXT_PRIMARY)};
    word-wrap: break-word;
    white-space: pre-wrap;
    line-height: 1.5;
    font-size: ${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px;
    position: relative;
    width: ${UI_DIMENSIONS.FIT_CONTENT};

    ${props =>
        (props.isUser
            ? `
        border-bottom-right-radius: ${UI_DIMENSIONS.BORDER_RADIUS_SMALL}px;
    `
            : `
        border-bottom-left-radius: ${UI_DIMENSIONS.BORDER_RADIUS_SMALL}px;
    `)}
`;

export const UserName = styled(Text)`
    font-weight: ${UI_DIMENSIONS.FONT_WEIGHT_MEDIUM};
    font-size: ${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px;
    color: ${UI_COLORS.TEXT_GRAY};
    line-height: 22px;
`;

const dotAnimation = keyframes`
    0%, 20% {
        opacity: ${ANIMATION_VALUES.OPACITY_HIDDEN};
    }
    50% {
        opacity: ${ANIMATION_VALUES.OPACITY_VISIBLE};
    }
    100% {
        opacity: ${ANIMATION_VALUES.OPACITY_HIDDEN};
    }
`;

export const ThinkingIndicator = styled.div`
    align-items: center;
    gap: ${UI_DIMENSIONS.SPACING_MEDIUM}px;
    font-size: ${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px;
    color: ${UI_COLORS.TEXT_SECONDARY};
    padding: ${UI_DIMENSIONS.PADDING_SMALL};
`;

export const EstimatedTime = styled.span`
    font-size: ${UI_DIMENSIONS.FONT_SIZE_SMALL}px;
    color: ${UI_COLORS.TEXT_TERTIARY};
    margin-left: ${UI_DIMENSIONS.SPACING_SMALL}px;
`;

export const DotLoading = styled.span`
    display: inline-flex;
    gap: 2px;

    &::after {
        content: "";
        display: inline-block;
        width: ${UI_DIMENSIONS.DOT_SIZE}px;
        height: ${UI_DIMENSIONS.DOT_SIZE}px;
        border-radius: ${CONFIG_VALUES.BORDER_RADIUS_50};
        background: currentColor;
        animation: ${dotAnimation} ${ANIMATION_VALUES.DOT_ANIMATION_DURATION} ${CONFIG_VALUES.ANIMATION_INFINITE};
    }

    &::before {
        content: "";
        display: inline-block;
        width: ${UI_DIMENSIONS.DOT_SIZE}px;
        height: ${UI_DIMENSIONS.DOT_SIZE}px;
        border-radius: ${CONFIG_VALUES.BORDER_RADIUS_50};
        background: currentColor;
        animation: ${dotAnimation} ${ANIMATION_VALUES.DOT_ANIMATION_DURATION} ${CONFIG_VALUES.ANIMATION_INFINITE};
        animation-delay: ${ANIMATION_VALUES.DOT_ANIMATION_DELAY_1};
        margin-right: 2px;
    }

    & {
        position: relative;
    }

    &::after {
        animation-delay: ${ANIMATION_VALUES.DOT_ANIMATION_DELAY_2};
    }
`;
